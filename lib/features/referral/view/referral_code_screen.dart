import 'package:auto_route/auto_route.dart';
import 'package:banachef/core/bloc/bloc_exports.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../../../core/di/injection_container.dart';
import '../../../ui/kit/ui_kit.dart';
import '../../../ui/responsive/responsive.dart';
import '../cubit/referral_cubit.dart';
import '../cubit/referral_state.dart';
import '../widgets/referral_header.dart';
import '../widgets/referral_benefits.dart';
import '../widgets/referral_input_section.dart';

@RoutePage(name: 'ReferralCodeRoute')
class ReferralCodeScreen extends BaseCubitView<ReferralCubit> {
  const ReferralCodeScreen({super.key});

  @override
  ReferralCubit createCubit(BuildContext context) {
    return getIt<ReferralCubit>();
  }

  @override
  Widget buildContent(BuildContext context, dynamic state) {
    return const _ReferralCodeContent();
  }


}

class _ReferralCodeContent extends StatefulWidget {
  const _ReferralCodeContent();

  @override
  State<_ReferralCodeContent> createState() => _ReferralCodeContentState();
}

class _ReferralCodeContentState extends State<_ReferralCodeContent> {
  final TextEditingController _codeController = TextEditingController();

  @override
  void dispose() {
    _codeController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        // Gradient background giống onboarding
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              AppColors.onboardingGradientStart, // Xanh lá rất nhạt
              AppColors.lightBackground,         // Trắng kem
            ],
            stops: [0.0, 0.3], // Gradient chỉ ở 30% trên cùng
          ),
        ),
        child: SafeArea(
          child: BlocListener<ReferralCubit, ReferralState>(
            listener: (context, state) {
              if (state is ReferralSuccess) {
                setState(() {
                  _isCodeApplied = true;
                });
                _codeFocusNode.unfocus();

                // Show success snackbar
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text(state.message ?? 'Áp dụng mã thành công!'),
                    backgroundColor: const Color(0xFF6ECB63),
                    behavior: SnackBarBehavior.floating,
                  ),
                );
              } else if (state is ReferralError) {
                // Show error snackbar
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text(state.message),
                    backgroundColor: Theme.of(context).colorScheme.error,
                    behavior: SnackBarBehavior.floating,
                  ),
                );
              }
            },
        child: GestureDetector(
          onTap: () {
            // Hide keyboard when tapping outside
            FocusScope.of(context).unfocus();
          },
          child: Column(
            children: [
              // Referral Illustration - Full width và top
              _buildReferralIllustration(context),

              // Content với padding
              Expanded(
                child: ResponsiveContainer(
                  padding: context.pagePadding,
                  child: SingleChildScrollView(
                    child: Column(
                      children: [


                      // Description
                      Padding(
                        padding: EdgeInsets.symmetric(horizontal: context.spacing(2)),
                        child: Text(
                          'Bạn có mã giới thiệu không?\nNhập mã để nhận ngay ưu đãi độc quyền!',
                          style: context.headlineSmall.copyWith(
                            color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.9),
                            height: 1.4,
                            fontWeight: FontWeight.w600,
                          ),
                          textAlign: TextAlign.center,
                        ),
                      ),
                      
                      VSpace.xl(),
                      
                      // Input Field
                      _buildInputField(context),
                      
                      VSpace.lg(),
                      
                      // Apply Button
                      _buildApplyButton(context),

                      VSpace.xs(),

                      // Skip Button
                      _buildSkipButton(context),

                      VSpace.xl(),
                      ],
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
      )));
  }

  Widget _buildInputField(BuildContext context) {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: context.spacing(1)),
      child: Container(
        decoration: BoxDecoration(
          color: Theme.of(context).colorScheme.surface,
          borderRadius: BorderRadius.circular(16),
          boxShadow: [
            BoxShadow(
              color: Theme.of(context).colorScheme.shadow.withValues(alpha: 0.05),
              blurRadius: 8,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: TextField(
          controller: _codeController,
          focusNode: _codeFocusNode,
          enabled: !_isCodeApplied,
          textAlign: TextAlign.center,
          textCapitalization: TextCapitalization.characters, // Mặc định in hoa
          inputFormatters: [
            // Chuyển đổi tất cả thành chữ hoa
            TextInputFormatter.withFunction((oldValue, newValue) {
              return newValue.copyWith(text: newValue.text.toUpperCase());
            }),
          ],
          style: context.titleLarge.copyWith(
            fontWeight: FontWeight.w700,
            letterSpacing: 3,
            color: Theme.of(context).colorScheme.onSurface,
          ),
          decoration: InputDecoration(
            hintText: 'Nhập mã của bạn',
            hintStyle: context.bodyLarge.copyWith(
              color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.4),
              fontSize: 16,
              fontWeight: FontWeight.w400,
              letterSpacing: 1,
            ),
            filled: true,
            fillColor: Theme.of(context).colorScheme.surface,
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(16),
              borderSide: BorderSide(
                color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.2),
                width: 2,
              ),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(16),
              borderSide: BorderSide(
                color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.2),
                width: 2,
              ),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(16),
              borderSide: const BorderSide(
                color: Color(0xFF6ECB63),
                width: 2,
              ),
            ),
            disabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(16),
              borderSide: const BorderSide(
                color: Color(0xFF6ECB63),
                width: 2,
              ),
            ),
            contentPadding: const EdgeInsets.symmetric(
              horizontal: 24,
              vertical: 20,
            ),
            prefixIcon: Icon(
              Icons.card_giftcard_rounded,
              size: 24,
              color: _isCodeApplied
                  ? const Color(0xFF6ECB63)
                  : Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.6),
            ),
            suffixIcon: _isCodeApplied
                ? const Icon(
                    Icons.check_circle_rounded,
                    size: 24,
                    color: Color(0xFF6ECB63),
                  )
                : null,
          ),
          onChanged: (value) {
            // Reset state when user types and trigger rebuild for button state
            if (_isCodeApplied) {
              setState(() {
                _isCodeApplied = false;
              });
              context.read<ReferralCubit>().reset();
            } else {
              setState(() {}); // Trigger rebuild to update button state
            }
          },
        ),
      ),
    );
  }

  Widget _buildApplyButton(BuildContext context) {
    return BlocBuilder<ReferralCubit, ReferralState>(
      builder: (context, state) {
        final isLoading = state is ReferralLoading;
        final canApply = _codeController.text.trim().isNotEmpty && !_isCodeApplied && !isLoading;

        return Padding(
          padding: EdgeInsets.symmetric(horizontal: context.spacing(1)),
          child: AppButton(
            text: _isCodeApplied ? 'Đã áp dụng' : 'Áp dụng mã',
            onPressed: canApply ? () => _applyCode(context) : null,
            type: _isCodeApplied ? AppButtonType.secondary : AppButtonType.primary,
            size: AppButtonSize.large,
            isFullWidth: true,
            isLoading: isLoading,
            backgroundColor: _isCodeApplied
                ? const Color(0xFF6ECB63)
                : canApply
                    ? const Color(0xFF6ECB63)
                    : null,
            textColor: _isCodeApplied || canApply ? Colors.white : null,
            icon: _isCodeApplied
                ? const Icon(Icons.check_rounded, size: 20, color: Colors.white)
                : isLoading
                    ? null
                    : canApply
                        ? const Icon(Icons.redeem_rounded, size: 20, color: Colors.white)
                        : const Icon(Icons.redeem_rounded, size: 20),
          ),
        );
      },
    );
  }



  void _applyCode(BuildContext context) {
    final code = _codeController.text.trim();
    if (code.isNotEmpty) {
      context.read<ReferralCubit>().applyReferralCode(code);
    }
  }

  Widget _buildSkipButton(BuildContext context) {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: context.spacing(1)),
      child: TextButton(
        onPressed: () {
          // TODO: Navigate to next screen or home
          context.router.pushPath('/dashboard');
        },
        style: TextButton.styleFrom(
          padding: const EdgeInsets.symmetric(vertical: 16, horizontal: 24),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
        ),
        child: Text(
          'Bỏ qua, khám phá ngay',
          style: context.bodyLarge.copyWith(
            color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.8),
            fontWeight: FontWeight.w600,
            decoration: TextDecoration.underline,
            decorationColor: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.6),
          ),
        ),
      ),
    );
  }



  Widget _buildReferralIllustration(BuildContext context) {
    return SizedBox(
      width: double.infinity,
      height: context.responsive(ResponsiveValue(
        mobile: 280,
        tablet: 320,
        desktop: 360,
      )),
      child: Stack(
        children: [
          // SVG Illustration - Full width
          Positioned.fill(
            child: Assets.images.app.referralIllus.svg(
              fit: BoxFit.cover,
              width: double.infinity,
            ),
          ),

          // Gradient fade overlay at bottom
          Positioned(
            left: 0,
            right: 0,
            bottom: 0,
            height: 80,
            child: Container(
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                  colors: [
                    Theme.of(context).scaffoldBackgroundColor.withValues(alpha: 0.0),
                    Theme.of(context).scaffoldBackgroundColor.withValues(alpha: 0.3),
                    Theme.of(context).scaffoldBackgroundColor.withValues(alpha: 0.7),
                    Theme.of(context).scaffoldBackgroundColor,
                  ],
                  stops: const [0.0, 0.3, 0.7, 1.0],
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
