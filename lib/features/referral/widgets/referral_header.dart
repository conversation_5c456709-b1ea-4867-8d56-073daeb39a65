import 'package:flutter/material.dart';
import '../../../ui/kit/ui_kit.dart';
import '../../../shared/assets/assets.gen.dart';

/// Header component cho màn hình Referral
/// 
/// Hi<PERSON>n thị mascot Chef <PERSON><PERSON> và tiêu đề thân thiện
/// theo thiế<PERSON> kế BanaChef Design System
class ReferralHeader extends StatelessWidget {
  const ReferralHeader({super.key});

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        // Mascot Chef Bana illustration
        Container(
          width: 120,
          height: 120,
          margin: BanaSpacing.all.lg,
          child: Assets.images.app.referralIllus.svg(
            width: 120,
            height: 120,
            fit: BoxFit.contain,
          ),
        ),
        
        BanaSpacing.verticalSpacing.md,
        
        // Tiêu đề thân thiện
        Text(
          'Có ai giới thiệu bạn không?',
          style: BanaTypography.title2.copyWith(
            color: BanaColors.text,
            fontWeight: FontWeight.bold,
          ),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }
}
