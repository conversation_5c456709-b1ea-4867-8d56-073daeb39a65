import 'package:flutter/material.dart';
import '../../../ui/kit/ui_kit.dart';

/// Component giải thích lợi ích của việc nhập mã giới thiệu
/// 
/// Hi<PERSON>n thị thông tin về ưu đãi giảm giá 5$ với highlight
/// theo thiết kế BanaChef Design System
class ReferralBenefits extends StatelessWidget {
  const ReferralBenefits({super.key});

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: BanaSpacing.horizontal.lg,
      child: RichText(
        textAlign: TextAlign.center,
        text: TextSpan(
          style: BanaTypography.bodyLarge.copyWith(
            color: BanaColors.text,
            height: 1.5,
          ),
          children: [
            const TextSpan(
              text: 'Nhập mã giới thiệu để nhận ngay ưu đãi ',
            ),
            TextSpan(
              text: 'giảm giá 5\$',
              style: BanaTypography.bodyLarge.copyWith(
                color: BanaColors.primary,
                fontWeight: FontWeight.bold,
                height: 1.5,
              ),
            ),
            const TextSpan(
              text: ' cho l<PERSON><PERSON> nâng cấp BanaChef Premium đầu tiên của bạn.',
            ),
          ],
        ),
      ),
    );
  }
}
