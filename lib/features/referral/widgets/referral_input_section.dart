import 'package:flutter/material.dart';
import '../../../ui/kit/ui_kit.dart';
import '../cubit/referral_state.dart';

/// Component khu vực nhập liệu cho mã giới thiệu
/// 
/// Hỗ trợ các trạng thái: default, loading, success, error
/// theo thiết kế BanaChef Design System
class ReferralInputSection extends StatefulWidget {
  /// Controller cho text field
  final TextEditingController controller;
  
  /// Callback khi nhấn nút áp dụng
  final VoidCallback? onApply;
  
  /// Trạng thái hiện tại của referral
  final ReferralState state;
  
  /// Callback khi text thay đổi
  final ValueChanged<String>? onChanged;

  const ReferralInputSection({
    super.key,
    required this.controller,
    required this.state,
    this.onApply,
    this.onChanged,
  });

  @override
  State<ReferralInputSection> createState() => _ReferralInputSectionState();
}

class _ReferralInputSectionState extends State<ReferralInputSection> {
  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        // Text Field
        _buildTextField(),
        
        BanaSpacing.verticalSpacing.lg,
        
        // Apply Button
        _buildApplyButton(),
      ],
    );
  }

  Widget _buildTextField() {
    final isSuccess = widget.state is ReferralSuccess;
    final isError = widget.state is ReferralError;
    final isLoading = widget.state is ReferralLoading;
    
    String? errorText;
    if (isError) {
      errorText = (widget.state as ReferralError).message;
    }

    return BanaTextField(
      controller: widget.controller,
      hint: 'Nhập mã, ví dụ: BANACHEF-AN123',
      enabled: !isLoading && !isSuccess,
      hasError: isError,
      errorText: errorText,
      onChanged: widget.onChanged,
      prefixIcon: isSuccess ? Icons.check_circle : Icons.card_giftcard,
      textInputAction: TextInputAction.done,
      onSubmitted: (_) {
        if (widget.onApply != null && !isLoading && !isSuccess) {
          widget.onApply!();
        }
      },
    );
  }

  Widget _buildApplyButton() {
    final isSuccess = widget.state is ReferralSuccess;
    final isLoading = widget.state is ReferralLoading;
    final hasText = widget.controller.text.trim().isNotEmpty;
    
    if (isSuccess) {
      return _buildSuccessMessage();
    }
    
    return BanaButton.primary(
      text: 'Áp dụng Mã',
      onPressed: hasText && !isLoading ? widget.onApply : null,
      isLoading: isLoading,
      isFullWidth: true,
      size: BanaButtonSize.large,
    );
  }

  Widget _buildSuccessMessage() {
    return Container(
      width: double.infinity,
      padding: BanaSpacing.all.lg,
      decoration: BoxDecoration(
        color: BanaColors.success.withOpacity(0.1),
        borderRadius: BanaBorders.radius.lg,
        border: Border.all(
          color: BanaColors.success.withOpacity(0.3),
          width: 1,
        ),
      ),
      child: Row(
        children: [
          Icon(
            Icons.check_circle,
            color: BanaColors.success,
            size: 24,
          ),
          
          BanaSpacing.horizontalSpacing.md,
          
          Expanded(
            child: Text(
              'Thành công! Ưu đãi 5\$ đã được thêm vào tài khoản của bạn.',
              style: BanaTypography.bodyMedium.copyWith(
                color: BanaColors.success,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
